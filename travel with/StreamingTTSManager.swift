//
//  StreamingTTSManager.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import AVFoundation

/// 情感片段数据结构
struct EmotionSegment {
    let text: String
    let emotion: CanCanEmotion
    let order: Int
}

/// 流式TTS播放管理器
/// 实现按情感标签分割的流式TTS合成和播放，支持每个片段使用不同情感
@MainActor
class StreamingTTSManager: NSObject, ObservableObject {
    
    // MARK: - 属性
    
    /// TTS服务实例
    private let ttsService: TTSService
    
    /// 当前播放状态
    @Published var isPlaying: Bool = false

    /// 当前播放的音频播放器
    private var currentPlayer: AVAudioPlayer?

    // 并行处理相关属性
    private var parallelSegments: [ParallelTTSSegment] = []
    private var currentSegmentIndex: Int = 0
    private var totalSegments: Int = 0
    private let segmentLock = NSLock()

    // 并行TTS片段数据结构
    private struct ParallelTTSSegment {
        let id: String // reqid，用于标识每个请求
        let text: String
        let index: Int // 播放顺序索引
        let emotion: CanCanEmotion
        var audioData: Data?
        var isCompleted: Bool = false
        var isFailed: Bool = false

        init(id: String, text: String, index: Int, emotion: CanCanEmotion) {
            self.id = id
            self.text = text
            self.index = index
            self.emotion = emotion
        }
    }
    
    /// 播放完成回调
    var onPlaybackCompleted: (() -> Void)?
    
    /// 播放进度回调
    var onPlaybackProgress: ((Int, Int) -> Void)? // (当前句子, 总句子数)
    
    // MARK: - 初始化
    
    init(ttsService: TTSService) {
        self.ttsService = ttsService
        super.init()
    }
    
    // MARK: - 公共方法
    
    /// 开始并行流式播放文本
    /// - Parameters:
    ///   - text: 要播放的完整文本
    ///   - emotion: TTS情感
    func startStreamingPlayback(_ text: String, emotion: CanCanEmotion) async {
        print("🎵 开始并行流式TTS播放...")
        print("🎭 StreamingTTS使用情感: \(emotion.description) (\(emotion.rawValue))")
        print("📝 原始文本长度: \(text.count) 字符")
        print("📝 原始文本内容: \(text.prefix(100))...")

        // 重置状态并清理之前的连接
        await resetPlaybackState()
        await cleanupPreviousConnections()

        // 按情感标签分割文本为TTS片段
        let emotionSegments = splitTextByEmotionTags(text, defaultEmotion: emotion)
        totalSegments = emotionSegments.count
        currentSegmentIndex = 0

        print("📝 文本已按情感标签分割为 \(emotionSegments.count) 个片段")

        // 打印每个片段的详细信息
        for (index, segment) in emotionSegments.enumerated() {
            let segmentData = segment.text.data(using: .utf8) ?? Data()
            print("📝 片段 \(index + 1): 长度=\(segment.text.count)字符, 字节=\(segmentData.count), 情感=\(segment.emotion.rawValue), 内容=\(segment.text.prefix(50))...")
        }

        // 开始并行处理情感片段
        await processEmotionSegmentsParallel(emotionSegments)
    }

    /// 停止播放
    func stopPlayback() async {
        print("🛑 停止并行TTS播放...")

        // 停止当前播放
        currentPlayer?.stop()
        currentPlayer = nil

        // 重置状态
        await resetPlaybackState()

        print("✅ 并行TTS播放已停止")
    }
    
    /// 停止播放（同步版本，用于兼容）
    func stopPlayback() {
        Task {
            await stopPlayback()
        }
    }
    
    /// 暂停播放
    func pausePlayback() {
        currentPlayer?.pause()
        print("⏸️ 暂停流式TTS播放")
    }
    
    /// 恢复播放
    func resumePlayback() {
        currentPlayer?.play()
        print("▶️ 恢复流式TTS播放")
    }
    
    // MARK: - 私有方法
    
    /// 重置播放状态
    private func resetPlaybackState() async {
        // 停止当前播放
        currentPlayer?.stop()
        currentPlayer = nil

        // 清空并行片段
        segmentLock.lock()
        parallelSegments.removeAll()
        segmentLock.unlock()

        // 重置状态
        isPlaying = false
        currentSegmentIndex = 0
        totalSegments = 0
    }

    /// 清理之前的WebSocket连接，避免延迟响应
    private func cleanupPreviousConnections() async {
        print("🧹 清理之前的WebSocket连接...")

        // 停止当前播放
        await ttsService.stopPlayback()

        // 短暂等待，确保连接清理完成
        try? await Task.sleep(nanoseconds: 500_000_000) // 500ms

        print("✅ WebSocket连接清理完成")
    }

    /// 按情感标签分割文本为TTS片段
    private func splitTextByEmotionTags(_ text: String, defaultEmotion: CanCanEmotion) -> [EmotionSegment] {
        print("🎭 开始按情感标签分割文本...")

        var segments: [EmotionSegment] = []
        var remainingText = text
        var currentOrder = 0

        // 正则表达式匹配 </情感/> 格式的标签
        let pattern = #"(.*?)</([^/>]+)/>"#

        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators])

            while !remainingText.isEmpty {
                let range = NSRange(location: 0, length: remainingText.utf16.count)

                if let match = regex.firstMatch(in: remainingText, options: [], range: range) {
                    // 提取文本内容和情感标签
                    let textRange = Range(match.range(at: 1), in: remainingText)!
                    let emotionRange = Range(match.range(at: 2), in: remainingText)!

                    let segmentText = String(remainingText[textRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                    let emotionString = String(remainingText[emotionRange])

                    // 转换为CanCanEmotion
                    let emotion = CanCanEmotion(rawValue: emotionString) ?? defaultEmotion

                    if !segmentText.isEmpty {
                        segments.append(EmotionSegment(text: segmentText, emotion: emotion, order: currentOrder))
                        currentOrder += 1
                        print("🎭 提取片段 \(currentOrder): \"\(segmentText.prefix(30))...\" -> \(emotion.rawValue)")
                    }

                    // 移除已处理的部分
                    let fullMatchRange = Range(match.range, in: remainingText)!
                    remainingText = String(remainingText[fullMatchRange.upperBound...])
                } else {
                    // 没有更多标签，处理剩余文本
                    let finalText = remainingText.trimmingCharacters(in: .whitespacesAndNewlines)
                    if !finalText.isEmpty {
                        segments.append(EmotionSegment(text: finalText, emotion: defaultEmotion, order: currentOrder))
                        print("🎭 最终片段: \"\(finalText.prefix(30))...\" -> \(defaultEmotion.rawValue)")
                    }
                    break
                }
            }
        } catch {
            print("❌ 情感标签解析失败: \(error)")
            // 回退到整个文本使用默认情感
            segments.append(EmotionSegment(text: text, emotion: defaultEmotion, order: 0))
        }

        // 如果没有找到任何情感标签，使用默认情感处理整个文本
        if segments.isEmpty {
            let cleanText = text.trimmingCharacters(in: .whitespacesAndNewlines)
            if !cleanText.isEmpty {
                segments.append(EmotionSegment(text: cleanText, emotion: defaultEmotion, order: 0))
                print("🎭 未找到情感标签，使用默认情感: \(defaultEmotion.rawValue)")
            }
        }

        print("🎭 情感分割完成，共 \(segments.count) 个片段")
        return segments
    }

    /// 智能分割文本为TTS片段（按1024字节限制，在句号处断开）
    private func splitTextIntoTTSSegments(_ text: String) -> [String] {
        // 清理文本，移除多余的空白字符和特殊符号
        var cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // 移除表情符号和特殊字符，但保留基本标点
        cleanedText = cleanedText.replacingOccurrences(of: "～", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "✨", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "💫", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "🌪️", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "🍂", with: "")
        cleanedText = cleanedText.replacingOccurrences(of: "⊃｡•́‿•̀｡)⊃", with: "")

        // 如果整个文本小于1024字节，直接返回
        let textData = cleanedText.data(using: .utf8) ?? Data()
        if textData.count <= 1024 {
            print("📝 文本长度 \(textData.count) 字节，无需分割")
            return [cleanedText]
        }

        print("📝 文本长度 \(textData.count) 字节，需要智能分割")

        var segments: [String] = []
        var remainingText = cleanedText

        while !remainingText.isEmpty {
            let remainingData = remainingText.data(using: .utf8) ?? Data()

            if remainingData.count <= 1024 {
                // 剩余文本小于1024字节，直接添加
                segments.append(remainingText)
                print("📝 最后片段: \(remainingText.count) 字符, \(remainingData.count) 字节")
                break
            }

            // 找到1024字节内的最后一个句号位置
            let maxBytes = 1024
            var cutPosition = 0
            var byteCount = 0
            var lastPeriodPosition = -1

            for (index, char) in remainingText.enumerated() {
                let charData = String(char).data(using: .utf8) ?? Data()
                byteCount += charData.count

                if byteCount > maxBytes {
                    break
                }

                // 记录句号位置（包括中英文句号）
                if char == "。" || char == "." || char == "！" || char == "!" || char == "？" || char == "?" {
                    lastPeriodPosition = index
                }

                cutPosition = index
            }

            // 如果找到句号，在句号后切断；否则在最大字节位置切断
            let finalCutPosition: Int
            if lastPeriodPosition >= 0 {
                finalCutPosition = lastPeriodPosition + 1
                print("📝 在句号位置切断: 字符位置 \(finalCutPosition)")
            } else {
                finalCutPosition = cutPosition
                print("📝 在最大字节位置切断: 字符位置 \(finalCutPosition)")
            }

            let segment = String(remainingText.prefix(finalCutPosition)).trimmingCharacters(in: .whitespacesAndNewlines)
            if !segment.isEmpty {
                segments.append(segment)
                let segmentData = segment.data(using: .utf8) ?? Data()
                print("📝 分割片段: \(segment.count) 字符, \(segmentData.count) 字节")
            }

            remainingText = String(remainingText.dropFirst(finalCutPosition)).trimmingCharacters(in: .whitespacesAndNewlines)
        }

        print("📝 文本分割完成，共 \(segments.count) 个片段")
        return segments
    }


    /// 并行处理情感片段（每个片段使用对应的情感）
    private func processEmotionSegmentsParallel(_ emotionSegments: [EmotionSegment]) async {
        guard !emotionSegments.isEmpty else {
            print("⚠️ 没有有效的情感片段需要播放")
            return
        }

        isPlaying = true

        // 初始化并行片段数组
        segmentLock.lock()
        parallelSegments = emotionSegments.map { segment in
            ParallelTTSSegment(
                id: UUID().uuidString, // 生成唯一的reqid
                text: segment.text,
                index: segment.order,
                emotion: segment.emotion
            )
        }
        segmentLock.unlock()

        print("🚀 开始串行合成 \(emotionSegments.count) 个情感片段...")

        // 改为串行处理，避免WebSocket连接冲突
        for segment in parallelSegments {
            await synthesizeSegmentParallel(segment)
            // 添加短暂延迟，确保请求不会冲突
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        }

        print("🎵 所有情感片段合成请求已发送，开始按顺序播放...")

        // 按顺序播放片段
        await playSegmentsInOrder()

        print("✅ 所有情感片段处理完成")

        // 播放完成
        await MainActor.run {
            isPlaying = false
            onPlaybackCompleted?()
        }
    }

    /// 并行处理片段（并行合成，按顺序播放）
    private func processSegmentsParallel(_ segments: [String], emotion: CanCanEmotion) async {
        guard !segments.isEmpty else {
            print("⚠️ 没有有效的片段需要播放")
            return
        }

        isPlaying = true

        // 初始化并行片段数组
        segmentLock.lock()
        parallelSegments = segments.enumerated().map { index, text in
            ParallelTTSSegment(
                id: UUID().uuidString, // 生成唯一的reqid
                text: text,
                index: index,
                emotion: emotion
            )
        }
        segmentLock.unlock()

        print("🚀 开始串行合成 \(segments.count) 个片段...")

        // 改为串行处理，避免WebSocket连接冲突
        for segment in parallelSegments {
            await synthesizeSegmentParallel(segment)
            // 添加短暂延迟，确保请求不会冲突
            try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
        }

        print("🎵 所有片段合成请求已发送，开始按顺序播放...")

        // 按顺序播放片段
        await playSegmentsInOrder()

        print("✅ 所有片段处理完成")

        // 播放完成
        await MainActor.run {
            isPlaying = false
            onPlaybackCompleted?()
        }
    }
    
    /// 并行合成单个片段
    private func synthesizeSegmentParallel(_ segment: ParallelTTSSegment) async {
        print("🎤 并行合成片段 \(segment.index + 1)/\(totalSegments): \(segment.text.prefix(30))...")
        print("🆔 使用reqid: \(segment.id)")
        print("🎭 使用情感: \(segment.emotion.description) (\(segment.emotion.rawValue))")

        // 简化逻辑：直接调用TTS服务，不重试
        let audioData = await ttsService.synthesizeTextToDataWithReqId(
            text: segment.text,
            emotion: segment.emotion,
            reqId: segment.id
        )

        // 更新片段数据
        segmentLock.lock()
        if let index = parallelSegments.firstIndex(where: { $0.id == segment.id }) {
            if let audioData = audioData, !audioData.isEmpty {
                parallelSegments[index].audioData = audioData
                parallelSegments[index].isCompleted = true
                print("✅ 片段 \(segment.index + 1) 合成完成，音频大小: \(audioData.count) 字节")
                print("🎵 音频数据详情: 片段=\(segment.text.prefix(20))..., 大小=\(audioData.count)字节, reqid=\(segment.id)")
            } else {
                parallelSegments[index].isFailed = true
                parallelSegments[index].isCompleted = true
                print("❌ 片段 \(segment.index + 1) 合成返回空数据")
            }
        }
        segmentLock.unlock()
    }

    /// 按顺序播放片段（等待合成完成后播放）
    private func playSegmentsInOrder() async {
        for index in 0..<totalSegments {
            print("🔄 等待片段 \(index + 1) 合成完成...")

            // 等待当前片段合成完成
            while true {
                segmentLock.lock()
                let segment = parallelSegments.first { $0.index == index }
                let isCompleted = segment?.isCompleted ?? false
                let isFailed = segment?.isFailed ?? false
                segmentLock.unlock()

                if isCompleted || isFailed {
                    break
                }

                // 等待100ms后再检查
                try? await Task.sleep(nanoseconds: 100_000_000)
            }

            // 播放片段
            segmentLock.lock()
            if let segment = parallelSegments.first(where: { $0.index == index }) {
                segmentLock.unlock()

                if segment.isCompleted, let audioData = segment.audioData {
                    await playSegmentDirectly(segment.text, audioData: audioData, index: index)
                } else {
                    print("⚠️ 片段 \(index + 1) 合成失败，跳过播放")
                }
            } else {
                segmentLock.unlock()
                print("❌ 找不到片段 \(index + 1)")
            }
        }
    }

    /// 直接播放音频片段
    private func playSegmentDirectly(_ text: String, audioData: Data, index: Int) async {
        print("🔊 开始播放片段 \(index + 1): \(text.prefix(20))...")

        // 通知播放进度
        await MainActor.run {
            currentSegmentIndex = index
            onPlaybackProgress?(currentSegmentIndex + 1, totalSegments)
            print("📊 TTS播放进度: \(currentSegmentIndex + 1)/\(totalSegments)")
        }

        do {
            let player = try AVAudioPlayer(data: audioData)
            player.delegate = self

            // 🔧 修复重音问题：优化音频播放器配置
            player.volume = 0.85  // 适中音量，避免失真
            player.enableRate = false  // 禁用播放速率控制，避免音调变化
            player.prepareToPlay()  // 预先准备播放器

            // 等待播放完成
            await withCheckedContinuation { continuation in
                Task { @MainActor in
                    currentPlayer = player

                    // 设置播放完成回调
                    var observer: NSObjectProtocol?
                    observer = NotificationCenter.default.addObserver(
                        forName: NSNotification.Name("AudioPlayerDidFinish"),
                        object: player,
                        queue: .main
                    ) { _ in
                        if let observer = observer {
                            NotificationCenter.default.removeObserver(observer)
                        }
                        continuation.resume()
                    }

                    player.play()
                }
            }

            print("✅ 片段 \(index + 1) 播放完成")

        } catch {
            print("❌ 播放片段 \(index + 1) 失败: \(error)")
        }
    }
    

    

    
    /// 播放完成处理
    private func playbackCompleted() async {
        print("🎉 并行流式TTS播放完成")

        isPlaying = false
        currentPlayer = nil
        currentSegmentIndex = 0
        totalSegments = 0

        // 清空并行片段
        segmentLock.lock()
        parallelSegments.removeAll()
        segmentLock.unlock()

        // 调用完成回调
        onPlaybackCompleted?()
    }
}

// MARK: - AVAudioPlayerDelegate

extension StreamingTTSManager: AVAudioPlayerDelegate {
    
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        print("✅ 片段 \(currentSegmentIndex + 1) 播放完成")

        // 发送播放完成通知
        NotificationCenter.default.post(
            name: NSNotification.Name("AudioPlayerDidFinish"),
            object: player
        )
    }

    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        print("❌ 音频播放解码错误: \(error?.localizedDescription ?? "未知错误")")

        // 发送播放完成通知（即使出错也要继续）
        NotificationCenter.default.post(
            name: NSNotification.Name("AudioPlayerDidFinish"),
            object: player
        )
    }
}



// MARK: - TTS服务扩展

extension TTSService {

    /// 合成文本为音频数据
    func synthesizeText(_ text: String, emotion: CanCanEmotion) async -> Data? {
        return await synthesizeTextToData(text: text, emotion: emotion)
    }
}
