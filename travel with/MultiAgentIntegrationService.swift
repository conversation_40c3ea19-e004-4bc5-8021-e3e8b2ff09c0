//
//  MultiAgentIntegrationService.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation
import UIKit
import SwiftUI

// MARK: - 多智能体集成服务
/// 将多智能体系统集成到现有的AIService中，提供无缝的用户体验
@MainActor
class MultiAgentIntegrationService: ObservableObject {
    
    // MARK: - 属性
    
    /// 共享状态中心
    private let sharedStateHub: SharedStateHub
    
    /// 智能体调度器
    private let agentScheduler: AgentScheduler

    /// JSON任务调度器
    private let jsonTaskScheduler: JSONTaskScheduler

    /// 流式TTS管理器
    private let streamingTTSManager: StreamingTTSManager

    /// 原有的AI服务（用于兼容性）
    private let legacyAIService: AIService
    
    /// 是否启用多智能体系统
    @Published var isMultiAgentEnabled: Bool = true
    
    /// 系统状态
    @Published var systemStatus: SystemStatus = .initializing
    
    /// 当前处理的智能体信息
    @Published var currentProcessingAgents: [AgentIdentifier] = []
    
    /// 处理统计
    @Published var processingStats: ProcessingStats = ProcessingStats()
    
    // MARK: - 初始化
    
    init(legacyAIService: AIService) {
        self.legacyAIService = legacyAIService
        
        print("🔧 初始化多智能体集成服务...")
        
        // 初始化共享状态中心
        self.sharedStateHub = SharedStateHub()
        
        // 初始化智能体调度器
        self.agentScheduler = AgentScheduler(sharedState: sharedStateHub)

        // 初始化JSON任务调度器
        self.jsonTaskScheduler = JSONTaskScheduler(sharedState: sharedStateHub, agentScheduler: agentScheduler)

        // 初始化流式TTS管理器
        self.streamingTTSManager = StreamingTTSManager(ttsService: TTSService.shared)
        
        // 启动初始化流程
        Task {
            await initializeSystem()
        }
    }
    
    /// 初始化系统
    private func initializeSystem() async {
        print("🔄 开始初始化多智能体集成系统...")
        
        systemStatus = .initializing
        
        // 等待共享状态中心就绪
        while !sharedStateHub.isSystemReady() {
            try? await Task.sleep(nanoseconds: 500_000_000) // 等待500ms
        }
        
        // 等待调度器就绪
        while !agentScheduler.isReady {
            try? await Task.sleep(nanoseconds: 500_000_000) // 等待500ms
        }
        
        // 集成历史记录
        await integrateHistoryData()
        
        systemStatus = .ready
        print("✅ 多智能体集成系统初始化完成！")
    }
    
    /// 集成历史记录数据
    private func integrateHistoryData() async {
        print("📚 集成历史记录数据...")
        
        // 将现有的消息历史同步到共享状态中心
        for message in legacyAIService.messages {
            let contextMessage = ContextMessage(
                userMessage: message.isFromUser ? message.content : "",
                aiResponse: message.isFromUser ? nil : message.content,
                timestamp: message.timestamp
            )
            
            if message.isFromUser {
                sharedStateHub.currentContext.addMessage(
                    userMessage: message.content,
                    aiResponse: nil
                )
            }
        }
        
        print("✅ 历史记录集成完成")
    }
    
    // MARK: - 消息处理方法
    
    /// 处理文本消息（多智能体版本）
    func processTextMessage(_ text: String) async -> AIResponse {
        guard isMultiAgentEnabled && systemStatus == .ready else {
            // 回退到原有服务
            return await fallbackToLegacyService(text: text)
        }
        
        print("🎯 多智能体系统处理文本消息: \(text.prefix(50))...")
        
        // 更新处理状态
        systemStatus = .processing
        processingStats.totalRequests += 1
        let startTime = Date()
        
        // 创建用户输入
        let userInput = UserInput(message: text, messageType: .text)

        // 第一步：使用任务分配智能体获取JSON决策
        let taskAssignmentInput = AgentInput(
            userMessage: text,
            messageType: .text,
            imageData: nil,
            context: sharedStateHub.currentContext
        )

        guard let taskAssignmentAgent = agentScheduler.agents[.taskAssignment] else {
            print("❌ 任务分配智能体不可用，回退到原有流程")
            let response = await agentScheduler.processUserInput(userInput)
            systemStatus = .ready
            return response
        }

        // 获取任务分配决策
        let taskAssignmentOutput = await taskAssignmentAgent.process(taskAssignmentInput)

        // 解析JSON决策
        guard let taskDecisionJSON = taskAssignmentOutput.metadata["task_decision_json"] as? String,
              let taskDecision = parseTaskDecisionJSON(taskDecisionJSON) else {
            print("❌ JSON决策解析失败，回退到原有流程")
            let response = await agentScheduler.processUserInput(userInput)
            systemStatus = .ready
            return response
        }

        print("📋 任务分配决策: 聊天=沟通智能体")

        // 第二步：使用JSON任务调度器并行执行
        let response = await jsonTaskScheduler.executeTasksFromJSON(taskDecision, userInput: userInput)

        // 更新处理统计
        let processingTime = Date().timeIntervalSince(startTime)
        processingStats.totalProcessingTime += processingTime
        processingStats.averageProcessingTime = processingStats.totalProcessingTime / Double(processingStats.totalRequests)
        currentProcessingAgents = response.executedAgents // 直接使用AgentIdentifier数组

        systemStatus = .ready

        print("✅ JSON多智能体处理完成，耗时: \(String(format: "%.2f", processingTime))秒")

        // 启动流式TTS播放
        if let ttsEmotion = response.ttsEmotion {
            Task {
                await startStreamingTTSPlayback(text: response.textForTTS, emotion: ttsEmotion)
            }
        }

        return response
    }

    // 注意：后台行动规划逻辑已移除，现在由JSON任务调度器统一管理

    /// 处理图像消息（多智能体版本）
    func processImageMessage(_ image: UIImage, withText text: String = "") async -> AIResponse {
        guard isMultiAgentEnabled && systemStatus == .ready else {
            // 回退到原有服务
            return await fallbackToLegacyService(image: image, text: text)
        }
        
        print("🖼️ 多智能体系统处理图像消息...")
        
        systemStatus = .processing
        processingStats.totalRequests += 1
        let startTime = Date()
        
        // 压缩图像数据
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            return AIResponse(content: "图像处理失败", processingTime: 0)
        }
        
        // 创建用户输入
        let userInput = UserInput(
            message: text.isEmpty ? "请分析这张图片" : text,
            messageType: .image,
            imageData: imageData
        )
        
        // 使用智能体调度器处理
        let response = await agentScheduler.processUserInput(userInput)
        
        // 更新处理统计
        let processingTime = Date().timeIntervalSince(startTime)
        processingStats.totalProcessingTime += processingTime
        processingStats.averageProcessingTime = processingStats.totalProcessingTime / Double(processingStats.totalRequests)
        currentProcessingAgents = response.executedAgents
        
        systemStatus = .ready
        
        print("✅ 多智能体图像处理完成，耗时: \(String(format: "%.2f", processingTime))秒")
        
        return response
    }
    
    /// 处理表情消息（多智能体版本）
    func processEmojiMessage(_ emoji: String) async -> AIResponse {
        guard isMultiAgentEnabled && systemStatus == .ready else {
            // 回退到原有服务
            return await fallbackToLegacyService(emoji: emoji)
        }
        
        print("😊 多智能体系统处理表情消息: \(emoji)")
        
        systemStatus = .processing
        processingStats.totalRequests += 1
        let startTime = Date()
        
        // 创建用户输入
        let userInput = UserInput(message: emoji, messageType: .emoji)
        
        // 使用智能体调度器处理
        let response = await agentScheduler.processUserInput(userInput)
        
        // 更新处理统计
        let processingTime = Date().timeIntervalSince(startTime)
        processingStats.totalProcessingTime += processingTime
        processingStats.averageProcessingTime = processingStats.totalProcessingTime / Double(processingStats.totalRequests)
        currentProcessingAgents = response.executedAgents
        
        systemStatus = .ready
        
        print("✅ 多智能体表情处理完成，耗时: \(String(format: "%.2f", processingTime))秒")
        
        return response
    }
    
    // MARK: - 回退方法
    
    /// 回退到原有服务（文本）
    private func fallbackToLegacyService(text: String) async -> AIResponse {
        print("⚠️ 回退到原有AI服务处理文本消息")
        
        let startTime = Date()
        let response = await legacyAIService.sendMessage(text, useRolePlayingModel: true)
        let processingTime = Date().timeIntervalSince(startTime)
        
        return AIResponse(
            content: response ?? "抱歉，我现在有点忙不过来",
            processingTime: processingTime,
            executedAgents: [],
            metadata: ["fallback": true, "service": "legacy"]
        )
    }
    
    /// 回退到原有服务（图像）
    private func fallbackToLegacyService(image: UIImage, text: String) async -> AIResponse {
        print("⚠️ 回退到原有AI服务处理图像消息")
        
        let startTime = Date()
        // 这里需要调用原有的图像处理逻辑
        // 由于原有服务的图像处理比较复杂，这里简化处理
        let response = await legacyAIService.sendMessage("用户发送了一张图片：\(text)", useRolePlayingModel: true)
        let processingTime = Date().timeIntervalSince(startTime)
        
        return AIResponse(
            content: response ?? "我看到了你的图片，但现在处理有点问题",
            processingTime: processingTime,
            executedAgents: [],
            metadata: ["fallback": true, "service": "legacy", "type": "image"]
        )
    }
    
    /// 回退到原有服务（表情）
    private func fallbackToLegacyService(emoji: String) async -> AIResponse {
        print("⚠️ 回退到原有AI服务处理表情消息")
        
        let startTime = Date()
        let response = await legacyAIService.sendMessage("我发了个表情：\(emoji)，你随便回个什么", useRolePlayingModel: true)
        let processingTime = Date().timeIntervalSince(startTime)
        
        return AIResponse(
            content: response ?? "😊",
            processingTime: processingTime,
            executedAgents: [],
            metadata: ["fallback": true, "service": "legacy", "type": "emoji"]
        )
    }
    
    // MARK: - 状态查询方法
    
    /// 获取系统状态摘要
    func getSystemStatusSummary() -> String {
        let hubStatus = sharedStateHub.getSystemStatusSummary()
        let schedulerStatus = agentScheduler.getStatusSummary()
        
        return """
        === 多智能体集成系统状态 ===
        系统状态: \(systemStatus.description)
        多智能体模式: \(isMultiAgentEnabled ? "启用" : "禁用")
        
        \(hubStatus)
        
        \(schedulerStatus)
        
        === 处理统计 ===
        总请求数: \(processingStats.totalRequests)
        平均处理时间: \(String(format: "%.2f", processingStats.averageProcessingTime))秒
        当前处理智能体: \(currentProcessingAgents.map { $0.displayName }.joined(separator: ", "))
        """
    }
    
    /// 切换多智能体模式
    func toggleMultiAgentMode() {
        isMultiAgentEnabled.toggle()
        print("🔄 多智能体模式已\(isMultiAgentEnabled ? "启用" : "禁用")")
    }

    /// 解析任务决策JSON
    private func parseTaskDecisionJSON(_ jsonString: String) -> TaskDecision? {
        // 清理JSON字符串
        let cleanedJSON = jsonString
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        guard let jsonData = cleanedJSON.data(using: .utf8) else {
            print("❌ JSON字符串转换失败")
            return nil
        }

        do {
            let decision = try JSONDecoder().decode(TaskDecision.self, from: jsonData)
            return decision
        } catch {
            print("❌ JSON解析失败: \(error)")
            return nil
        }
    }

    /// 启动流式TTS播放
    private func startStreamingTTSPlayback(text: String, emotion: CanCanEmotion) async {
        print("🎵 启动流式TTS播放...")
        print("🎭 MultiAgent推荐TTS情感: \(emotion.description) (\(emotion.rawValue))")
        print("📝 TTS播放文本长度: \(text.count) 字符")
        print("📝 TTS播放文本预览: \(text.prefix(100))...")

        // 设置播放回调
        streamingTTSManager.onPlaybackCompleted = { [weak self] in
            Task { @MainActor in
                self?.handleTTSPlaybackCompleted()
            }
        }

        streamingTTSManager.onPlaybackProgress = { [weak self] current, total in
            Task { @MainActor in
                self?.handleTTSPlaybackProgress(current: current, total: total)
            }
        }

        // 开始流式播放
        await streamingTTSManager.startStreamingPlayback(text, emotion: emotion)
    }

    /// 处理TTS播放完成
    private func handleTTSPlaybackCompleted() {
        print("🎉 流式TTS播放完成")

        // 通知外部系统TTS播放完成，可以重新开始语音监听
        NotificationCenter.default.post(
            name: NSNotification.Name("TTSPlaybackCompleted"),
            object: nil
        )

        print("📢 已发送TTS播放完成通知")
    }

    /// 处理TTS播放进度
    private func handleTTSPlaybackProgress(current: Int, total: Int) {
        print("📊 TTS播放进度: \(current)/\(total)")
        // 这里可以更新UI进度指示器
    }

    /// 停止流式TTS播放
    func stopStreamingTTSPlayback() {
        streamingTTSManager.stopPlayback()
    }

    /// 暂停流式TTS播放
    func pauseStreamingTTSPlayback() {
        streamingTTSManager.pausePlayback()
    }

    /// 恢复流式TTS播放
    func resumeStreamingTTSPlayback() {
        streamingTTSManager.resumePlayback()
    }
}

// MARK: - 数据模型

/// 系统状态
enum SystemStatus {
    case initializing   // 初始化中
    case ready         // 就绪
    case processing    // 处理中
    case error         // 错误
    
    var description: String {
        switch self {
        case .initializing: return "初始化中"
        case .ready: return "就绪"
        case .processing: return "处理中"
        case .error: return "错误"
        }
    }
}

/// 处理统计
struct ProcessingStats {
    var totalRequests: Int = 0
    var totalProcessingTime: TimeInterval = 0
    var averageProcessingTime: TimeInterval = 0
}
